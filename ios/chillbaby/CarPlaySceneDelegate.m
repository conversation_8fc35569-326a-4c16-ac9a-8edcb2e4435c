#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>
#import "RNCarPlay.h"  

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;

    // Tell react-native-carplay CarPlay is connected
    [RNCarPlay connectWithInterfaceController:interfaceController window:templateApplicationScene.carWindow];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    // Clear the interface controller
    self.interfaceController = nil;

    // Notify React Native that Car<PERSON>lay is disconnected
    [RNCarPlay disconnect];
}

// - (void)notifyReactNativeCarPlayConnected:(BOOL)connected {
//     // This method will notify React Native about CarPlay connection status
//     // The react-native-carplay library should handle this automatically
//     NSLog(@"CarPlay connection status: %@", connected ? @"Connected" : @"Disconnected");
// }

@end
